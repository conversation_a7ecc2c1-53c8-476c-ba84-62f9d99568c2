import { defineComponent, computed, useContext, useAsync, ref, onMounted } from '@nuxtjs/composition-api';
import { Breadcrumb, Input, Menu } from 'ant-design-vue';
import moment from 'moment';
import Fuse from 'fuse.js';
import styles from './guide-manual.module.less';
import { MENUCONFIG } from './config/guide-manual.config';
import PreNextArticle from './widgets/pre-next-article';
import IconArrow from '@/assets/icons/icon-wenzilianjiantou.svg?inline';

const GuideManual = defineComponent({
  name: 'GuideManual',
  layout: 'embed',
  // middleware: 'auth',
  setup() {
    const { $content, error, params } = useContext();

    const selectedMenu = computed(() => [params.value.slug]);
    const fuseHandler = ref(null);
    const searchList = ref([]);

    const flatMenu = MENUCONFIG.reduce((acc, cur) => {
      if (cur.children) {
        acc.push(
          cur,
          ...cur.children.map((v) => ({
            ...v,
            parent: cur,
          }))
        );
      } else {
        acc.push(cur);
      }
      return acc;
    }, [] as any[]);

    const selectedMenuItem = computed(
      () => flatMenu.find((menu) => selectedMenu.value.includes(menu.key)) || {}
    );

    const openKeys = ref(
      selectedMenuItem.value ? [selectedMenuItem.value.parent?.key || selectedMenuItem.value.key] : []
    );

    const isRootSelected = (key) => {
      return openKeys.value.includes(params.value.slug) && params.value.slug === key;
    };

    const prevNextSelected = computed(() => {
      if (selectedMenuItem.value.isRoot) return null;
      const index = flatMenu.findIndex((item) => item.key === params.value.slug);
      return {
        prev: flatMenu[index - 1]?.isRoot ? null : flatMenu[index - 1],
        next: flatMenu[index + 1]?.isRoot ? null : flatMenu[index + 1],
      };
    });

    const pageData = useAsync(async () => {
      try {
        const content = await $content('manuals', params.value.slug).fetch<any>();
        const [prev, next] = await $content('manuals').surround(params.value.slug).fetch<any>();
        return {
          content,
          prev,
          next,
        };
      } catch (err) {
        error({
          statusCode: 404,
        });
      }
    }, params.value.slug);

    const updateDate = computed(() => {
      return moment(pageData.value?.content?.updateDate).format('YYYY-MM-DD');
    });

    const stripMarkdown = (s: string) =>
      (s || '')
        // 1. 去掉图片语法 ![alt](url)
        .replace(/!\[[^\]]*]\([^)]*\)/g, ' ')
        // 2. 链接 [text](url) -> text
        .replace(/\[([^\]]+)]\([^)]*\)/g, '$1')
        // 3. 行内/块代码 `code` or ```code```
        .replace(/`{1,3}[^`]*`{1,3}/g, ' ')
        // 4. 标题 # ## ### ...
        .replace(/^#{1,6}\s*/gm, '')
        // 5. 引用 >
        .replace(/^>\s*/gm, '')
        // 6. 强调符号 * _ ~ -
        .replace(/[*_~\-]+/g, ' ')
        // 7. 去掉 HTML 标签 <tag ...> 或 </tag>
        .replace(/<\/?[^>]+>/g, '')
        // 8. 多余空格折叠
        .replace(/\s{2,}/g, ' ')
        // 9. 首尾空格
        .trim();

    const initSearch = async () => {
      const searchData = await $content('manuals', { text: true }).only(['text', 'slug']).fetch<any>();
      console.log(searchData);
      const fuse = new Fuse(searchData, {
        keys: ['text'],
        minMatchCharLength: 2,
        includeScore: true,
        includeMatches: true,
      });

      fuseHandler.value = fuse;
    };

    const handleSearch = (keywords) => {
      if (keywords?.trim()?.length < 2) return;
      const res = fuseHandler.value.search(keywords);
      console.log(res);
      searchList.value = res.map((v) => {
        return {
          ...v.item,
          title: flatMenu.find((menu) => menu.key === v.item.slug).label,
        };
      });
    };

    onMounted(() => {
      initSearch();
    });

    return {
      selectedMenu,
      selectedMenuItem,
      openKeys,
      pageData,
      updateDate,
      prevNextSelected,
      isRootSelected,
      handleSearch,
      searchList,
    };
  },
  head: {
    title: '用户手册',
    titleTemplate: '%s - 第三方风险排查系统',
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.sideBar}>
          <div class={styles.pageHead}>用户手册</div>
          <Input.Search placeholder="请输入关键词" onSearch={this.handleSearch} />
          <ul>
            {this.searchList.map((item) => {
              return (
                <li key={item.slug}>
                  <nuxt-link to={item.slug}>{item.title}</nuxt-link>
                </li>
              );
            })}
          </ul>
          <Menu
            style={styles.munu}
            mode="inline"
            defaultSelectedKeys={this.selectedMenu}
            defaultOpenKeys={this.openKeys}
            inlineIndent={15}
            onOpenChange={(keys) => {
              this.openKeys = keys;
            }}
          >
            {MENUCONFIG.map((menu) => {
              if (menu.children) {
                return (
                  <Menu.SubMenu key={menu.key} class={{ 'submenu-selected': this.isRootSelected(menu.key) }}>
                    <div slot="title" class={styles.subMenu}>
                      <div class="menu-icon">
                        <IconArrow class="menu-icon__arrow" />
                      </div>
                      <nuxt-link to={menu.path}>{menu.label}</nuxt-link>
                    </div>
                    {menu.children.map((child) => (
                      <Menu.Item class={styles.menuItem} key={child.key}>
                        <div class="menu-icon">
                          <div class="menu-icon__dot"></div>
                        </div>
                        <nuxt-link to={child.path}>{child.label}</nuxt-link>
                      </Menu.Item>
                    ))}
                  </Menu.SubMenu>
                );
              }
              return (
                <Menu.Item class={styles.menuItem} key={menu.key}>
                  <div class={styles.icon}>
                    <div class={styles.dot}></div>
                  </div>
                  <nuxt-link to={menu.path}>{menu.label}</nuxt-link>
                </Menu.Item>
              );
            })}
          </Menu>
        </div>
        <div class={styles.main}>
          <div class={styles.wrapper}>
            <Breadcrumb class={styles.breadcurmb}>
              {this.selectedMenuItem.parent?.path ? (
                <Breadcrumb.Item>
                  <nuxt-link to={this.selectedMenuItem.parent.path}>
                    {this.selectedMenuItem.parent.label}
                  </nuxt-link>
                </Breadcrumb.Item>
              ) : null}
              <Breadcrumb.Item>{this.selectedMenuItem.label}</Breadcrumb.Item>
            </Breadcrumb>
            <div style={{ flex: 1 }}>
              <div class={styles.doc}>
                <nuxt-content document={this?.pageData?.content} />
              </div>
              {this.selectedMenuItem.isRoot && this.selectedMenuItem.children ? (
                <ul class={styles.quickLink}>
                  {this.selectedMenuItem.children.map((child) => (
                    <li>
                      <nuxt-link to={child.path}>{child.label}</nuxt-link>
                    </li>
                  ))}
                </ul>
              ) : null}
            </div>
            <div class={styles.timeInfo}>
              最后更新于 {moment(this?.pageData?.content?.updateDate).format('YYYY-MM-DD')}
            </div>
            <PreNextArticle prev={this.prevNextSelected?.prev} next={this.prevNextSelected?.next} />
          </div>
        </div>
      </div>
    );
  },
});
export default GuideManual;
